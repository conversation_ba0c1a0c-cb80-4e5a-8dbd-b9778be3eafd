{"name": "react-lazy-with-preload", "version": "2.2.1", "description": "Wraps the React.lazy API with preload functionality", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ianschmitz/react-lazy-with-preload.git"}, "bugs": {"url": "https://github.com/ianschmitz/react-lazy-with-preload/issues"}, "homepage": "https://github.com/ianschmitz/react-lazy-with-preload#readme", "keywords": ["React", "Lazy", "Preload"], "scripts": {"build": "tsc -p tsconfig.build.json", "lint": "eslint -f codeframe --ext .js,.ts,.tsx src/", "test": "jest", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/core": "^7.10.2", "@babel/preset-env": "^7.10.2", "@babel/preset-react": "^7.10.1", "@babel/preset-typescript": "^7.10.1", "@testing-library/react": "^13.3.0", "@types/jest": "^28.1.7", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "@typescript-eslint/eslint-plugin": "^3.2.0", "@typescript-eslint/parser": "^3.2.0", "babel-jest": "^28.1.3", "eslint": "^7.2.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-react": "^7.20.0", "husky": "^4.2.5", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "jest-watch-typeahead": "^2.0.0", "prettier": "^2.0.5", "pretty-quick": "^2.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "~4.7.4"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}}