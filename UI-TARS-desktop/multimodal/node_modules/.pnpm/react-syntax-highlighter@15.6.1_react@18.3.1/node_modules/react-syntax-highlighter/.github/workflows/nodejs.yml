name: Node CI

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      token: d37b4f28-c0ae-4546-81c9-0487a264db20
    strategy:
      matrix:
        node-version: [14.x]

    steps:
    - uses: actions/checkout@v1
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
    - name: npm install, build, and test
      run: |
        npm install
        npm run build --if-present
        npm run lint
        npm test
        npm run publish-coverage
