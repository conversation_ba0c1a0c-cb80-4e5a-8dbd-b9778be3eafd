export default {
    "code[class*=\"language-\"]": {
        "color": "#fff",
        "textShadow": "0 1px 1px #000",
        "fontFamily": "Menlo, Monaco, \"Courier New\", monospace",
        "direction": "ltr",
        "textAlign": "left",
        "wordSpacing": "normal",
        "whiteSpace": "pre",
        "wordWrap": "normal",
        "lineHeight": "1.4",
        "background": "none",
        "border": "0",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none"
    },
    "pre[class*=\"language-\"]": {
        "color": "#fff",
        "textShadow": "0 1px 1px #000",
        "fontFamily": "Menlo, Monaco, \"Courier New\", monospace",
        "direction": "ltr",
        "textAlign": "left",
        "wordSpacing": "normal",
        "whiteSpace": "pre",
        "wordWrap": "normal",
        "lineHeight": "1.4",
        "background": "#222",
        "border": "0",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "padding": "15px",
        "margin": "1em 0",
        "overflow": "auto",
        "MozBorderRadius": "8px",
        "WebkitBorderRadius": "8px",
        "borderRadius": "8px"
    },
    "pre[class*=\"language-\"] code": {
        "float": "left",
        "padding": "0 15px 0 0"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "background": "#222",
        "padding": "5px 10px",
        "lineHeight": "1",
        "MozBorderRadius": "3px",
        "WebkitBorderRadius": "3px",
        "borderRadius": "3px"
    },
    "comment": {
        "color": "#797979"
    },
    "prolog": {
        "color": "#797979"
    },
    "doctype": {
        "color": "#797979"
    },
    "cdata": {
        "color": "#797979"
    },
    "selector": {
        "color": "#fff"
    },
    "operator": {
        "color": "#fff"
    },
    "punctuation": {
        "color": "#fff"
    },
    "namespace": {
        "Opacity": ".7"
    },
    "tag": {
        "color": "#ffd893"
    },
    "boolean": {
        "color": "#ffd893"
    },
    "atrule": {
        "color": "#B0C975"
    },
    "attr-value": {
        "color": "#B0C975"
    },
    "hex": {
        "color": "#B0C975"
    },
    "string": {
        "color": "#B0C975"
    },
    "property": {
        "color": "#c27628"
    },
    "entity": {
        "color": "#c27628",
        "cursor": "help"
    },
    "url": {
        "color": "#c27628"
    },
    "attr-name": {
        "color": "#c27628"
    },
    "keyword": {
        "color": "#c27628"
    },
    "regex": {
        "color": "#9B71C6"
    },
    "function": {
        "color": "#e5a638"
    },
    "constant": {
        "color": "#e5a638"
    },
    "variable": {
        "color": "#fdfba8"
    },
    "number": {
        "color": "#8799B0"
    },
    "important": {
        "color": "#E45734"
    },
    "deliminator": {
        "color": "#E45734"
    },
    ".line-highlight.line-highlight": {
        "background": "rgba(255, 255, 255, .2)"
    },
    ".line-highlight.line-highlight:before": {
        "top": ".3em",
        "backgroundColor": "rgba(255, 255, 255, .3)",
        "color": "#fff",
        "MozBorderRadius": "8px",
        "WebkitBorderRadius": "8px",
        "borderRadius": "8px"
    },
    ".line-highlight.line-highlight[data-end]:after": {
        "top": ".3em",
        "backgroundColor": "rgba(255, 255, 255, .3)",
        "color": "#fff",
        "MozBorderRadius": "8px",
        "WebkitBorderRadius": "8px",
        "borderRadius": "8px"
    },
    ".line-numbers .line-numbers-rows > span": {
        "borderRight": "3px #d9d336 solid"
    }
}