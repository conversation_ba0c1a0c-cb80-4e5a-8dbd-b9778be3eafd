export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#000080",
        "color": "#0ff"
    },
    "hljs-subst": {
        "color": "#0ff"
    },
    "hljs-string": {
        "color": "#ff0"
    },
    "hljs-attribute": {
        "color": "#ff0"
    },
    "hljs-symbol": {
        "color": "#ff0"
    },
    "hljs-bullet": {
        "color": "#ff0"
    },
    "hljs-built_in": {
        "color": "#ff0"
    },
    "hljs-builtin-name": {
        "color": "#ff0"
    },
    "hljs-template-tag": {
        "color": "#ff0"
    },
    "hljs-template-variable": {
        "color": "#ff0"
    },
    "hljs-addition": {
        "color": "#ff0"
    },
    "hljs-keyword": {
        "color": "#fff",
        "fontWeight": "bold"
    },
    "hljs-selector-tag": {
        "color": "#fff",
        "fontWeight": "bold"
    },
    "hljs-section": {
        "color": "#fff",
        "fontWeight": "bold"
    },
    "hljs-type": {
        "color": "#fff"
    },
    "hljs-name": {
        "color": "#fff",
        "fontWeight": "bold"
    },
    "hljs-selector-id": {
        "color": "#fff"
    },
    "hljs-selector-class": {
        "color": "#fff"
    },
    "hljs-variable": {
        "color": "#fff"
    },
    "hljs-comment": {
        "color": "#888"
    },
    "hljs-quote": {
        "color": "#888"
    },
    "hljs-doctag": {
        "color": "#888"
    },
    "hljs-deletion": {
        "color": "#888"
    },
    "hljs-number": {
        "color": "#0f0"
    },
    "hljs-regexp": {
        "color": "#0f0"
    },
    "hljs-literal": {
        "color": "#0f0"
    },
    "hljs-link": {
        "color": "#0f0"
    },
    "hljs-meta": {
        "color": "#008080"
    },
    "hljs-title": {
        "fontWeight": "bold"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    }
}