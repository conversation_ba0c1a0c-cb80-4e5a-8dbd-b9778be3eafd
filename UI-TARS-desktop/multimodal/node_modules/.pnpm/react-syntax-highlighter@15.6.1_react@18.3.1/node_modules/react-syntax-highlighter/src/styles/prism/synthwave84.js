export default {
    "code[class*=\"language-\"]": {
        "color": "#f92aad",
        "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3",
        "background": "none",
        "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
        "fontSize": "1em",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none"
    },
    "pre[class*=\"language-\"]": {
        "color": "#f92aad",
        "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3",
        "background": "none",
        "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
        "fontSize": "1em",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "lineHeight": "1.5",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto",
        "backgroundColor": "transparent !important",
        "backgroundImage": "linear-gradient(to bottom, #2a2139 75%, #34294f)"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "backgroundColor": "transparent !important",
        "backgroundImage": "linear-gradient(to bottom, #2a2139 75%, #34294f)",
        "padding": ".1em",
        "borderRadius": ".3em",
        "whiteSpace": "normal"
    },
    "comment": {
        "color": "#8e8e8e"
    },
    "block-comment": {
        "color": "#8e8e8e"
    },
    "prolog": {
        "color": "#8e8e8e"
    },
    "doctype": {
        "color": "#8e8e8e"
    },
    "cdata": {
        "color": "#8e8e8e"
    },
    "punctuation": {
        "color": "#ccc"
    },
    "tag": {
        "color": "#e2777a"
    },
    "attr-name": {
        "color": "#e2777a"
    },
    "namespace": {
        "color": "#e2777a"
    },
    "number": {
        "color": "#e2777a"
    },
    "unit": {
        "color": "#e2777a"
    },
    "hexcode": {
        "color": "#e2777a"
    },
    "deleted": {
        "color": "#e2777a"
    },
    "property": {
        "color": "#72f1b8",
        "textShadow": "0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475"
    },
    "selector": {
        "color": "#72f1b8",
        "textShadow": "0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475"
    },
    "function-name": {
        "color": "#6196cc"
    },
    "boolean": {
        "color": "#fdfdfd",
        "textShadow": "0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"
    },
    "selector.id": {
        "color": "#fdfdfd",
        "textShadow": "0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"
    },
    "function": {
        "color": "#fdfdfd",
        "textShadow": "0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"
    },
    "class-name": {
        "color": "#fff5f6",
        "textShadow": "0 0 2px #000, 0 0 10px #fc1f2c75, 0 0 5px #fc1f2c75, 0 0 25px #fc1f2c75"
    },
    "constant": {
        "color": "#f92aad",
        "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"
    },
    "symbol": {
        "color": "#f92aad",
        "textShadow": "0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"
    },
    "important": {
        "color": "#f4eee4",
        "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575",
        "fontWeight": "bold"
    },
    "atrule": {
        "color": "#f4eee4",
        "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
    },
    "keyword": {
        "color": "#f4eee4",
        "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
    },
    "selector.class": {
        "color": "#f4eee4",
        "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
    },
    "builtin": {
        "color": "#f4eee4",
        "textShadow": "0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"
    },
    "string": {
        "color": "#f87c32"
    },
    "char": {
        "color": "#f87c32"
    },
    "attr-value": {
        "color": "#f87c32"
    },
    "regex": {
        "color": "#f87c32"
    },
    "variable": {
        "color": "#f87c32"
    },
    "operator": {
        "color": "#67cdcc"
    },
    "entity": {
        "color": "#67cdcc",
        "cursor": "help"
    },
    "url": {
        "color": "#67cdcc"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "inserted": {
        "color": "green"
    }
}