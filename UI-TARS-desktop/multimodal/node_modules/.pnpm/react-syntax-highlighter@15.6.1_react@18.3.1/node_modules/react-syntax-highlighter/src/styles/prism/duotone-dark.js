export default {
    "code[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#2a2734",
        "color": "#9a86fd"
    },
    "pre[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#2a2734",
        "color": "#9a86fd",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto"
    },
    "pre > code[class*=\"language-\"]": {
        "fontSize": "1em"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    "pre[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    "pre[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    "code[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    "code[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#6a51e6"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "padding": ".1em",
        "borderRadius": ".3em"
    },
    "comment": {
        "color": "#6c6783"
    },
    "prolog": {
        "color": "#6c6783"
    },
    "doctype": {
        "color": "#6c6783"
    },
    "cdata": {
        "color": "#6c6783"
    },
    "punctuation": {
        "color": "#6c6783"
    },
    "namespace": {
        "Opacity": ".7"
    },
    "tag": {
        "color": "#e09142"
    },
    "operator": {
        "color": "#e09142"
    },
    "number": {
        "color": "#e09142"
    },
    "property": {
        "color": "#9a86fd"
    },
    "function": {
        "color": "#9a86fd"
    },
    "tag-id": {
        "color": "#eeebff"
    },
    "selector": {
        "color": "#eeebff"
    },
    "atrule-id": {
        "color": "#eeebff"
    },
    "code.language-javascript": {
        "color": "#c4b9fe"
    },
    "attr-name": {
        "color": "#c4b9fe"
    },
    "code.language-css": {
        "color": "#ffcc99"
    },
    "code.language-scss": {
        "color": "#ffcc99"
    },
    "boolean": {
        "color": "#ffcc99"
    },
    "string": {
        "color": "#ffcc99"
    },
    "entity": {
        "color": "#ffcc99",
        "cursor": "help"
    },
    "url": {
        "color": "#ffcc99"
    },
    ".language-css .token.string": {
        "color": "#ffcc99"
    },
    ".language-scss .token.string": {
        "color": "#ffcc99"
    },
    ".style .token.string": {
        "color": "#ffcc99"
    },
    "attr-value": {
        "color": "#ffcc99"
    },
    "keyword": {
        "color": "#ffcc99"
    },
    "control": {
        "color": "#ffcc99"
    },
    "directive": {
        "color": "#ffcc99"
    },
    "unit": {
        "color": "#ffcc99"
    },
    "statement": {
        "color": "#ffcc99"
    },
    "regex": {
        "color": "#ffcc99"
    },
    "atrule": {
        "color": "#ffcc99"
    },
    "placeholder": {
        "color": "#ffcc99"
    },
    "variable": {
        "color": "#ffcc99"
    },
    "deleted": {
        "textDecoration": "line-through"
    },
    "inserted": {
        "borderBottom": "1px dotted #eeebff",
        "textDecoration": "none"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "important": {
        "fontWeight": "bold",
        "color": "#c4b9fe"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "pre > code.highlight": {
        "Outline": ".4em solid #8a75f5",
        "OutlineOffset": ".4em"
    },
    ".line-numbers.line-numbers .line-numbers-rows": {
        "borderRightColor": "#2c2937"
    },
    ".line-numbers .line-numbers-rows > span:before": {
        "color": "#3c3949"
    },
    ".line-highlight.line-highlight": {
        "background": "linear-gradient(to right, rgba(224, 145, 66, 0.2) 70%, rgba(224, 145, 66, 0))"
    }
}