export default {
    "hljs-comment": {
        "color": "#989498"
    },
    "hljs-quote": {
        "color": "#989498"
    },
    "hljs-variable": {
        "color": "#dd464c"
    },
    "hljs-template-variable": {
        "color": "#dd464c"
    },
    "hljs-attribute": {
        "color": "#dd464c"
    },
    "hljs-tag": {
        "color": "#dd464c"
    },
    "hljs-name": {
        "color": "#dd464c"
    },
    "hljs-selector-id": {
        "color": "#dd464c"
    },
    "hljs-selector-class": {
        "color": "#dd464c"
    },
    "hljs-regexp": {
        "color": "#dd464c"
    },
    "hljs-link": {
        "color": "#dd464c"
    },
    "hljs-deletion": {
        "color": "#dd464c"
    },
    "hljs-number": {
        "color": "#fd8b19"
    },
    "hljs-built_in": {
        "color": "#fd8b19"
    },
    "hljs-builtin-name": {
        "color": "#fd8b19"
    },
    "hljs-literal": {
        "color": "#fd8b19"
    },
    "hljs-type": {
        "color": "#fd8b19"
    },
    "hljs-params": {
        "color": "#fd8b19"
    },
    "hljs-class .hljs-title": {
        "color": "#fdcc59"
    },
    "hljs-string": {
        "color": "#8fc13e"
    },
    "hljs-symbol": {
        "color": "#8fc13e"
    },
    "hljs-bullet": {
        "color": "#8fc13e"
    },
    "hljs-addition": {
        "color": "#8fc13e"
    },
    "hljs-meta": {
        "color": "#149b93"
    },
    "hljs-function": {
        "color": "#1290bf"
    },
    "hljs-section": {
        "color": "#1290bf"
    },
    "hljs-title": {
        "color": "#1290bf"
    },
    "hljs-keyword": {
        "color": "#c85e7c"
    },
    "hljs-selector-tag": {
        "color": "#c85e7c"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#322931",
        "color": "#b9b5b8",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}