export default {
    "code[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#322d29",
        "color": "#88786d"
    },
    "pre[class*=\"language-\"]": {
        "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
        "fontSize": "14px",
        "lineHeight": "1.375",
        "direction": "ltr",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "background": "#322d29",
        "color": "#88786d",
        "padding": "1em",
        "margin": ".5em 0",
        "overflow": "auto"
    },
    "pre > code[class*=\"language-\"]": {
        "fontSize": "1em"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    "pre[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    "pre[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    "code[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    "code[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "#6f5849"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "padding": ".1em",
        "borderRadius": ".3em"
    },
    "comment": {
        "color": "#6a5f58"
    },
    "prolog": {
        "color": "#6a5f58"
    },
    "doctype": {
        "color": "#6a5f58"
    },
    "cdata": {
        "color": "#6a5f58"
    },
    "punctuation": {
        "color": "#6a5f58"
    },
    "namespace": {
        "Opacity": ".7"
    },
    "tag": {
        "color": "#bfa05a"
    },
    "operator": {
        "color": "#bfa05a"
    },
    "number": {
        "color": "#bfa05a"
    },
    "property": {
        "color": "#88786d"
    },
    "function": {
        "color": "#88786d"
    },
    "tag-id": {
        "color": "#fff3eb"
    },
    "selector": {
        "color": "#fff3eb"
    },
    "atrule-id": {
        "color": "#fff3eb"
    },
    "code.language-javascript": {
        "color": "#a48774"
    },
    "attr-name": {
        "color": "#a48774"
    },
    "code.language-css": {
        "color": "#fcc440"
    },
    "code.language-scss": {
        "color": "#fcc440"
    },
    "boolean": {
        "color": "#fcc440"
    },
    "string": {
        "color": "#fcc440"
    },
    "entity": {
        "color": "#fcc440",
        "cursor": "help"
    },
    "url": {
        "color": "#fcc440"
    },
    ".language-css .token.string": {
        "color": "#fcc440"
    },
    ".language-scss .token.string": {
        "color": "#fcc440"
    },
    ".style .token.string": {
        "color": "#fcc440"
    },
    "attr-value": {
        "color": "#fcc440"
    },
    "keyword": {
        "color": "#fcc440"
    },
    "control": {
        "color": "#fcc440"
    },
    "directive": {
        "color": "#fcc440"
    },
    "unit": {
        "color": "#fcc440"
    },
    "statement": {
        "color": "#fcc440"
    },
    "regex": {
        "color": "#fcc440"
    },
    "atrule": {
        "color": "#fcc440"
    },
    "placeholder": {
        "color": "#fcc440"
    },
    "variable": {
        "color": "#fcc440"
    },
    "deleted": {
        "textDecoration": "line-through"
    },
    "inserted": {
        "borderBottom": "1px dotted #fff3eb",
        "textDecoration": "none"
    },
    "italic": {
        "fontStyle": "italic"
    },
    "important": {
        "fontWeight": "bold",
        "color": "#a48774"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "pre > code.highlight": {
        "Outline": ".4em solid #816d5f",
        "OutlineOffset": ".4em"
    },
    ".line-numbers.line-numbers .line-numbers-rows": {
        "borderRightColor": "#35302b"
    },
    ".line-numbers .line-numbers-rows > span:before": {
        "color": "#46403d"
    },
    ".line-highlight.line-highlight": {
        "background": "linear-gradient(to right, rgba(191, 160, 90, 0.2) 70%, rgba(191, 160, 90, 0))"
    }
}