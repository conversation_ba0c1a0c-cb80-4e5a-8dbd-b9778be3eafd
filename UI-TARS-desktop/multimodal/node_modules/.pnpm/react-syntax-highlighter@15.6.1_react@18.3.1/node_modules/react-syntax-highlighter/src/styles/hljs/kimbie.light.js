export default {
    "hljs-comment": {
        "color": "#a57a4c"
    },
    "hljs-quote": {
        "color": "#a57a4c"
    },
    "hljs-variable": {
        "color": "#dc3958"
    },
    "hljs-template-variable": {
        "color": "#dc3958"
    },
    "hljs-tag": {
        "color": "#dc3958"
    },
    "hljs-name": {
        "color": "#dc3958"
    },
    "hljs-selector-id": {
        "color": "#dc3958"
    },
    "hljs-selector-class": {
        "color": "#dc3958"
    },
    "hljs-regexp": {
        "color": "#dc3958"
    },
    "hljs-meta": {
        "color": "#dc3958"
    },
    "hljs-number": {
        "color": "#f79a32"
    },
    "hljs-built_in": {
        "color": "#f79a32"
    },
    "hljs-builtin-name": {
        "color": "#f79a32"
    },
    "hljs-literal": {
        "color": "#f79a32"
    },
    "hljs-type": {
        "color": "#f79a32"
    },
    "hljs-params": {
        "color": "#f79a32"
    },
    "hljs-deletion": {
        "color": "#f79a32"
    },
    "hljs-link": {
        "color": "#f79a32"
    },
    "hljs-title": {
        "color": "#f06431"
    },
    "hljs-section": {
        "color": "#f06431"
    },
    "hljs-attribute": {
        "color": "#f06431"
    },
    "hljs-string": {
        "color": "#889b4a"
    },
    "hljs-symbol": {
        "color": "#889b4a"
    },
    "hljs-bullet": {
        "color": "#889b4a"
    },
    "hljs-addition": {
        "color": "#889b4a"
    },
    "hljs-keyword": {
        "color": "#98676a"
    },
    "hljs-selector-tag": {
        "color": "#98676a"
    },
    "hljs-function": {
        "color": "#98676a"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#fbebd4",
        "color": "#84613d",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}