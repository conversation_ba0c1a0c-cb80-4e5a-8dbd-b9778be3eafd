export default {
    "hljs-comment": {
        "color": "#999999"
    },
    "hljs-quote": {
        "color": "#999999"
    },
    "hljs-variable": {
        "color": "#f2777a"
    },
    "hljs-template-variable": {
        "color": "#f2777a"
    },
    "hljs-tag": {
        "color": "#f2777a"
    },
    "hljs-name": {
        "color": "#f2777a"
    },
    "hljs-selector-id": {
        "color": "#f2777a"
    },
    "hljs-selector-class": {
        "color": "#f2777a"
    },
    "hljs-regexp": {
        "color": "#f2777a"
    },
    "hljs-deletion": {
        "color": "#f2777a"
    },
    "hljs-number": {
        "color": "#f99157"
    },
    "hljs-built_in": {
        "color": "#f99157"
    },
    "hljs-builtin-name": {
        "color": "#f99157"
    },
    "hljs-literal": {
        "color": "#f99157"
    },
    "hljs-type": {
        "color": "#f99157"
    },
    "hljs-params": {
        "color": "#f99157"
    },
    "hljs-meta": {
        "color": "#f99157"
    },
    "hljs-link": {
        "color": "#f99157"
    },
    "hljs-attribute": {
        "color": "#ffcc66"
    },
    "hljs-string": {
        "color": "#99cc99"
    },
    "hljs-symbol": {
        "color": "#99cc99"
    },
    "hljs-bullet": {
        "color": "#99cc99"
    },
    "hljs-addition": {
        "color": "#99cc99"
    },
    "hljs-title": {
        "color": "#6699cc"
    },
    "hljs-section": {
        "color": "#6699cc"
    },
    "hljs-keyword": {
        "color": "#cc99cc"
    },
    "hljs-selector-tag": {
        "color": "#cc99cc"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#2d2d2d",
        "color": "#cccccc",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}