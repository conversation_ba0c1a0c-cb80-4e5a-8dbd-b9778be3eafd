export default {
    "hljs-comment": {
        "color": "#78877d"
    },
    "hljs-quote": {
        "color": "#78877d"
    },
    "hljs-variable": {
        "color": "#b16139"
    },
    "hljs-template-variable": {
        "color": "#b16139"
    },
    "hljs-attribute": {
        "color": "#b16139"
    },
    "hljs-tag": {
        "color": "#b16139"
    },
    "hljs-name": {
        "color": "#b16139"
    },
    "hljs-regexp": {
        "color": "#b16139"
    },
    "hljs-link": {
        "color": "#b16139"
    },
    "hljs-selector-id": {
        "color": "#b16139"
    },
    "hljs-selector-class": {
        "color": "#b16139"
    },
    "hljs-number": {
        "color": "#9f713c"
    },
    "hljs-meta": {
        "color": "#9f713c"
    },
    "hljs-built_in": {
        "color": "#9f713c"
    },
    "hljs-builtin-name": {
        "color": "#9f713c"
    },
    "hljs-literal": {
        "color": "#9f713c"
    },
    "hljs-type": {
        "color": "#9f713c"
    },
    "hljs-params": {
        "color": "#9f713c"
    },
    "hljs-string": {
        "color": "#489963"
    },
    "hljs-symbol": {
        "color": "#489963"
    },
    "hljs-bullet": {
        "color": "#489963"
    },
    "hljs-title": {
        "color": "#478c90"
    },
    "hljs-section": {
        "color": "#478c90"
    },
    "hljs-keyword": {
        "color": "#55859b"
    },
    "hljs-selector-tag": {
        "color": "#55859b"
    },
    "hljs-deletion": {
        "color": "#171c19",
        "display": "inline-block",
        "width": "100%",
        "backgroundColor": "#b16139"
    },
    "hljs-addition": {
        "color": "#171c19",
        "display": "inline-block",
        "width": "100%",
        "backgroundColor": "#489963"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#171c19",
        "color": "#87928a",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}