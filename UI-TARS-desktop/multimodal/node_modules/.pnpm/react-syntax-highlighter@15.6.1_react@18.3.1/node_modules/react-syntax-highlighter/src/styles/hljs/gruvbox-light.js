export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#fbf1c7",
        "color": "#3c3836"
    },
    "hljs-subst": {
        "color": "#3c3836"
    },
    "hljs-deletion": {
        "color": "#9d0006"
    },
    "hljs-formula": {
        "color": "#9d0006"
    },
    "hljs-keyword": {
        "color": "#9d0006"
    },
    "hljs-link": {
        "color": "#9d0006"
    },
    "hljs-selector-tag": {
        "color": "#9d0006"
    },
    "hljs-built_in": {
        "color": "#076678"
    },
    "hljs-emphasis": {
        "color": "#076678",
        "fontStyle": "italic"
    },
    "hljs-name": {
        "color": "#076678"
    },
    "hljs-quote": {
        "color": "#076678"
    },
    "hljs-strong": {
        "color": "#076678",
        "fontWeight": "bold"
    },
    "hljs-title": {
        "color": "#076678"
    },
    "hljs-variable": {
        "color": "#076678"
    },
    "hljs-attr": {
        "color": "#b57614"
    },
    "hljs-params": {
        "color": "#b57614"
    },
    "hljs-template-tag": {
        "color": "#b57614"
    },
    "hljs-type": {
        "color": "#b57614"
    },
    "hljs-builtin-name": {
        "color": "#8f3f71"
    },
    "hljs-doctag": {
        "color": "#8f3f71"
    },
    "hljs-literal": {
        "color": "#8f3f71"
    },
    "hljs-number": {
        "color": "#8f3f71"
    },
    "hljs-code": {
        "color": "#af3a03"
    },
    "hljs-meta": {
        "color": "#af3a03"
    },
    "hljs-regexp": {
        "color": "#af3a03"
    },
    "hljs-selector-id": {
        "color": "#af3a03"
    },
    "hljs-template-variable": {
        "color": "#af3a03"
    },
    "hljs-addition": {
        "color": "#79740e"
    },
    "hljs-meta-string": {
        "color": "#79740e"
    },
    "hljs-section": {
        "color": "#79740e",
        "fontWeight": "bold"
    },
    "hljs-selector-attr": {
        "color": "#79740e"
    },
    "hljs-selector-class": {
        "color": "#79740e"
    },
    "hljs-string": {
        "color": "#79740e"
    },
    "hljs-symbol": {
        "color": "#79740e"
    },
    "hljs-attribute": {
        "color": "#427b58"
    },
    "hljs-bullet": {
        "color": "#427b58"
    },
    "hljs-class": {
        "color": "#427b58"
    },
    "hljs-function": {
        "color": "#427b58"
    },
    "hljs-function .hljs-keyword": {
        "color": "#427b58"
    },
    "hljs-meta-keyword": {
        "color": "#427b58"
    },
    "hljs-selector-pseudo": {
        "color": "#427b58"
    },
    "hljs-tag": {
        "color": "#427b58",
        "fontWeight": "bold"
    },
    "hljs-comment": {
        "color": "#928374",
        "fontStyle": "italic"
    },
    "hljs-link_label": {
        "color": "#8f3f71"
    }
}