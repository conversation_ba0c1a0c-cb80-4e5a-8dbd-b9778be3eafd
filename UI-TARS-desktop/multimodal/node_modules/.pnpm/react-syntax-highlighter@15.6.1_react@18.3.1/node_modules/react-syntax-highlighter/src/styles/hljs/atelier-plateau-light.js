export default {
    "hljs-comment": {
        "color": "#655d5d"
    },
    "hljs-quote": {
        "color": "#655d5d"
    },
    "hljs-variable": {
        "color": "#ca4949"
    },
    "hljs-template-variable": {
        "color": "#ca4949"
    },
    "hljs-attribute": {
        "color": "#ca4949"
    },
    "hljs-tag": {
        "color": "#ca4949"
    },
    "hljs-name": {
        "color": "#ca4949"
    },
    "hljs-regexp": {
        "color": "#ca4949"
    },
    "hljs-link": {
        "color": "#ca4949"
    },
    "hljs-selector-id": {
        "color": "#ca4949"
    },
    "hljs-selector-class": {
        "color": "#ca4949"
    },
    "hljs-number": {
        "color": "#b45a3c"
    },
    "hljs-meta": {
        "color": "#b45a3c"
    },
    "hljs-built_in": {
        "color": "#b45a3c"
    },
    "hljs-builtin-name": {
        "color": "#b45a3c"
    },
    "hljs-literal": {
        "color": "#b45a3c"
    },
    "hljs-type": {
        "color": "#b45a3c"
    },
    "hljs-params": {
        "color": "#b45a3c"
    },
    "hljs-string": {
        "color": "#4b8b8b"
    },
    "hljs-symbol": {
        "color": "#4b8b8b"
    },
    "hljs-bullet": {
        "color": "#4b8b8b"
    },
    "hljs-title": {
        "color": "#7272ca"
    },
    "hljs-section": {
        "color": "#7272ca"
    },
    "hljs-keyword": {
        "color": "#8464c4"
    },
    "hljs-selector-tag": {
        "color": "#8464c4"
    },
    "hljs-deletion": {
        "color": "#1b1818",
        "display": "inline-block",
        "width": "100%",
        "backgroundColor": "#ca4949"
    },
    "hljs-addition": {
        "color": "#1b1818",
        "display": "inline-block",
        "width": "100%",
        "backgroundColor": "#4b8b8b"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#f4ecec",
        "color": "#585050",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}