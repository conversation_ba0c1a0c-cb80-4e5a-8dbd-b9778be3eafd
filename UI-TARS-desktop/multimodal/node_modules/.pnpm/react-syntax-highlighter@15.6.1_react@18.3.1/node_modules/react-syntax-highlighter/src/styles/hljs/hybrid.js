export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#1d1f21",
        "color": "#c5c8c6"
    },
    "hljs::selection": {
        "background": "#373b41"
    },
    "hljs span::selection": {
        "background": "#373b41"
    },
    "hljs::-moz-selection": {
        "background": "#373b41"
    },
    "hljs span::-moz-selection": {
        "background": "#373b41"
    },
    "hljs-title": {
        "color": "#f0c674"
    },
    "hljs-name": {
        "color": "#f0c674"
    },
    "hljs-comment": {
        "color": "#707880"
    },
    "hljs-meta": {
        "color": "#707880"
    },
    "hljs-meta .hljs-keyword": {
        "color": "#707880"
    },
    "hljs-number": {
        "color": "#cc6666"
    },
    "hljs-symbol": {
        "color": "#cc6666"
    },
    "hljs-literal": {
        "color": "#cc6666"
    },
    "hljs-deletion": {
        "color": "#cc6666"
    },
    "hljs-link": {
        "color": "#cc6666"
    },
    "hljs-string": {
        "color": "#b5bd68"
    },
    "hljs-doctag": {
        "color": "#b5bd68"
    },
    "hljs-addition": {
        "color": "#b5bd68"
    },
    "hljs-regexp": {
        "color": "#b5bd68"
    },
    "hljs-selector-attr": {
        "color": "#b5bd68"
    },
    "hljs-selector-pseudo": {
        "color": "#b5bd68"
    },
    "hljs-attribute": {
        "color": "#b294bb"
    },
    "hljs-code": {
        "color": "#b294bb"
    },
    "hljs-selector-id": {
        "color": "#b294bb"
    },
    "hljs-keyword": {
        "color": "#81a2be"
    },
    "hljs-selector-tag": {
        "color": "#81a2be"
    },
    "hljs-bullet": {
        "color": "#81a2be"
    },
    "hljs-tag": {
        "color": "#81a2be"
    },
    "hljs-subst": {
        "color": "#8abeb7"
    },
    "hljs-variable": {
        "color": "#8abeb7"
    },
    "hljs-template-tag": {
        "color": "#8abeb7"
    },
    "hljs-template-variable": {
        "color": "#8abeb7"
    },
    "hljs-type": {
        "color": "#de935f"
    },
    "hljs-built_in": {
        "color": "#de935f"
    },
    "hljs-builtin-name": {
        "color": "#de935f"
    },
    "hljs-quote": {
        "color": "#de935f"
    },
    "hljs-section": {
        "color": "#de935f"
    },
    "hljs-selector-class": {
        "color": "#de935f"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}