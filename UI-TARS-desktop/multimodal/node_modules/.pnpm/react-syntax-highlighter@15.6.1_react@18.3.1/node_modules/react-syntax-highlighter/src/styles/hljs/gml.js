export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#222222",
        "color": "#C0C0C0"
    },
    "hljs-keyword": {
        "color": "#FFB871",
        "fontWeight": "bold"
    },
    "hljs-built_in": {
        "color": "#FFB871"
    },
    "hljs-literal": {
        "color": "#FF8080"
    },
    "hljs-symbol": {
        "color": "#58E55A"
    },
    "hljs-comment": {
        "color": "#5B995B"
    },
    "hljs-string": {
        "color": "#FFFF00"
    },
    "hljs-number": {
        "color": "#FF8080"
    },
    "hljs-attribute": {
        "color": "#C0C0C0"
    },
    "hljs-selector-tag": {
        "color": "#C0C0C0"
    },
    "hljs-doctag": {
        "color": "#C0C0C0"
    },
    "hljs-name": {
        "color": "#C0C0C0"
    },
    "hljs-bullet": {
        "color": "#C0C0C0"
    },
    "hljs-code": {
        "color": "#C0C0C0"
    },
    "hljs-addition": {
        "color": "#C0C0C0"
    },
    "hljs-regexp": {
        "color": "#C0C0C0"
    },
    "hljs-variable": {
        "color": "#C0C0C0"
    },
    "hljs-template-variable": {
        "color": "#C0C0C0"
    },
    "hljs-link": {
        "color": "#C0C0C0"
    },
    "hljs-selector-attr": {
        "color": "#C0C0C0"
    },
    "hljs-selector-pseudo": {
        "color": "#C0C0C0"
    },
    "hljs-type": {
        "color": "#C0C0C0"
    },
    "hljs-selector-id": {
        "color": "#C0C0C0"
    },
    "hljs-selector-class": {
        "color": "#C0C0C0"
    },
    "hljs-quote": {
        "color": "#C0C0C0"
    },
    "hljs-template-tag": {
        "color": "#C0C0C0"
    },
    "hljs-deletion": {
        "color": "#C0C0C0"
    },
    "hljs-title": {
        "color": "#C0C0C0"
    },
    "hljs-section": {
        "color": "#C0C0C0"
    },
    "hljs-function": {
        "color": "#C0C0C0"
    },
    "hljs-meta-keyword": {
        "color": "#C0C0C0"
    },
    "hljs-meta": {
        "color": "#C0C0C0"
    },
    "hljs-subst": {
        "color": "#C0C0C0"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}