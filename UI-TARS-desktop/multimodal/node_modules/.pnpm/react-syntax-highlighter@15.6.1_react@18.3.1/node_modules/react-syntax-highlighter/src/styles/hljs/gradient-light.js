export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "linear-gradient(142deg, rgba(255,253,141,1) 0%, rgba(252,183,255,1) 35%, rgba(144,236,255,1) 100%)",
        "color": "#250482"
    },
    "hljs-subtr": {
        "color": "#01958B"
    },
    "hljs-doctag": {
        "color": "#CB7200"
    },
    "hljs-meta": {
        "color": "#CB7200"
    },
    "hljs-comment": {
        "color": "#CB7200"
    },
    "hljs-quote": {
        "color": "#CB7200",
        "fontStyle": "italic"
    },
    "hljs-selector-tag": {
        "color": "#07BD5F",
        "fontWeight": "bold"
    },
    "hljs-selector-id": {
        "color": "#07BD5F",
        "fontWeight": "bold"
    },
    "hljs-template-tag": {
        "color": "#07BD5F",
        "fontWeight": "bold"
    },
    "hljs-regexp": {
        "color": "#07BD5F"
    },
    "hljs-attr": {
        "color": "#07BD5F"
    },
    "hljs-tag": {
        "color": "#07BD5F"
    },
    "hljs-params": {
        "color": "#43449F",
        "fontWeight": "bold"
    },
    "hljs-selector-class": {
        "color": "#43449F",
        "fontWeight": "bold"
    },
    "hljs-bullet": {
        "color": "#43449F"
    },
    "hljs-keyword": {
        "color": "#7D2801",
        "fontWeight": "bold"
    },
    "hljs-section": {
        "color": "#7D2801",
        "fontWeight": "bold"
    },
    "hljs-meta-keyword": {
        "color": "#7D2801"
    },
    "hljs-symbol": {
        "color": "#7D2801"
    },
    "hljs-type": {
        "color": "#7D2801"
    },
    "hljs-addition": {
        "color": "#296562"
    },
    "hljs-number": {
        "color": "#7F0096"
    },
    "hljs-link": {
        "color": "#7F0096"
    },
    "hljs-string": {
        "color": "#38c0ff"
    },
    "hljs-attribute": {
        "color": "#296562"
    },
    "hljs-variable": {
        "color": "#025C8F"
    },
    "hljs-template-variable": {
        "color": "#025C8F"
    },
    "hljs-builtin-name": {
        "color": "#529117"
    },
    "hljs-built_in": {
        "color": "#529117"
    },
    "hljs-formula": {
        "color": "#529117"
    },
    "hljs-name": {
        "color": "#529117"
    },
    "hljs-title": {
        "color": "#529117"
    },
    "hljs-class": {
        "color": "#529117"
    },
    "hljs-function": {
        "color": "#529117"
    },
    "hljs-selector-pseudo": {
        "color": "#AD13FF"
    },
    "hljs-deletion": {
        "color": "#AD13FF"
    },
    "hljs-literal": {
        "color": "#AD13FF"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}