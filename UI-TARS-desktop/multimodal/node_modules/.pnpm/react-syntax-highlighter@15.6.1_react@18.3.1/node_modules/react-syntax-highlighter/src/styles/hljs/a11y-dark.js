export default {
    "hljs-comment": {
        "color": "#d4d0ab"
    },
    "hljs-quote": {
        "color": "#d4d0ab"
    },
    "hljs-variable": {
        "color": "#ffa07a"
    },
    "hljs-template-variable": {
        "color": "#ffa07a"
    },
    "hljs-tag": {
        "color": "#ffa07a"
    },
    "hljs-name": {
        "color": "#ffa07a"
    },
    "hljs-selector-id": {
        "color": "#ffa07a"
    },
    "hljs-selector-class": {
        "color": "#ffa07a"
    },
    "hljs-regexp": {
        "color": "#ffa07a"
    },
    "hljs-deletion": {
        "color": "#ffa07a"
    },
    "hljs-number": {
        "color": "#f5ab35"
    },
    "hljs-built_in": {
        "color": "#f5ab35"
    },
    "hljs-builtin-name": {
        "color": "#f5ab35"
    },
    "hljs-literal": {
        "color": "#f5ab35"
    },
    "hljs-type": {
        "color": "#f5ab35"
    },
    "hljs-params": {
        "color": "#f5ab35"
    },
    "hljs-meta": {
        "color": "#f5ab35"
    },
    "hljs-link": {
        "color": "#f5ab35"
    },
    "hljs-attribute": {
        "color": "#ffd700"
    },
    "hljs-string": {
        "color": "#abe338"
    },
    "hljs-symbol": {
        "color": "#abe338"
    },
    "hljs-bullet": {
        "color": "#abe338"
    },
    "hljs-addition": {
        "color": "#abe338"
    },
    "hljs-title": {
        "color": "#00e0e0"
    },
    "hljs-section": {
        "color": "#00e0e0"
    },
    "hljs-keyword": {
        "color": "#dcc6e0"
    },
    "hljs-selector-tag": {
        "color": "#dcc6e0"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#2b2b2b",
        "color": "#f8f8f2",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}