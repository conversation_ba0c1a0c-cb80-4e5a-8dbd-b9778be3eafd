export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#222",
        "color": "#aaa"
    },
    "hljs-subst": {
        "color": "#aaa"
    },
    "hljs-section": {
        "color": "#fff",
        "fontWeight": "bold"
    },
    "hljs-comment": {
        "color": "#444"
    },
    "hljs-quote": {
        "color": "#444"
    },
    "hljs-meta": {
        "color": "#444"
    },
    "hljs-string": {
        "color": "#ffcc33"
    },
    "hljs-symbol": {
        "color": "#ffcc33"
    },
    "hljs-bullet": {
        "color": "#ffcc33"
    },
    "hljs-regexp": {
        "color": "#ffcc33"
    },
    "hljs-number": {
        "color": "#00cc66"
    },
    "hljs-addition": {
        "color": "#00cc66"
    },
    "hljs-built_in": {
        "color": "#32aaee"
    },
    "hljs-builtin-name": {
        "color": "#32aaee"
    },
    "hljs-literal": {
        "color": "#32aaee"
    },
    "hljs-type": {
        "color": "#32aaee"
    },
    "hljs-template-variable": {
        "color": "#32aaee"
    },
    "hljs-attribute": {
        "color": "#32aaee"
    },
    "hljs-link": {
        "color": "#32aaee"
    },
    "hljs-keyword": {
        "color": "#6644aa"
    },
    "hljs-selector-tag": {
        "color": "#6644aa"
    },
    "hljs-name": {
        "color": "#6644aa"
    },
    "hljs-selector-id": {
        "color": "#6644aa"
    },
    "hljs-selector-class": {
        "color": "#6644aa"
    },
    "hljs-title": {
        "color": "#bb1166"
    },
    "hljs-variable": {
        "color": "#bb1166"
    },
    "hljs-deletion": {
        "color": "#bb1166"
    },
    "hljs-template-tag": {
        "color": "#bb1166"
    },
    "hljs-doctag": {
        "fontWeight": "bold"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    }
}