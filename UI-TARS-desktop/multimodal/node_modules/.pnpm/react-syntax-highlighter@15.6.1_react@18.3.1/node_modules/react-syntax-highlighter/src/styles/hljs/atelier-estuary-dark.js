export default {
    "hljs-comment": {
        "color": "#878573"
    },
    "hljs-quote": {
        "color": "#878573"
    },
    "hljs-variable": {
        "color": "#ba6236"
    },
    "hljs-template-variable": {
        "color": "#ba6236"
    },
    "hljs-attribute": {
        "color": "#ba6236"
    },
    "hljs-tag": {
        "color": "#ba6236"
    },
    "hljs-name": {
        "color": "#ba6236"
    },
    "hljs-regexp": {
        "color": "#ba6236"
    },
    "hljs-link": {
        "color": "#ba6236"
    },
    "hljs-selector-id": {
        "color": "#ba6236"
    },
    "hljs-selector-class": {
        "color": "#ba6236"
    },
    "hljs-number": {
        "color": "#ae7313"
    },
    "hljs-meta": {
        "color": "#ae7313"
    },
    "hljs-built_in": {
        "color": "#ae7313"
    },
    "hljs-builtin-name": {
        "color": "#ae7313"
    },
    "hljs-literal": {
        "color": "#ae7313"
    },
    "hljs-type": {
        "color": "#ae7313"
    },
    "hljs-params": {
        "color": "#ae7313"
    },
    "hljs-string": {
        "color": "#7d9726"
    },
    "hljs-symbol": {
        "color": "#7d9726"
    },
    "hljs-bullet": {
        "color": "#7d9726"
    },
    "hljs-title": {
        "color": "#36a166"
    },
    "hljs-section": {
        "color": "#36a166"
    },
    "hljs-keyword": {
        "color": "#5f9182"
    },
    "hljs-selector-tag": {
        "color": "#5f9182"
    },
    "hljs-deletion": {
        "color": "#22221b",
        "display": "inline-block",
        "width": "100%",
        "backgroundColor": "#ba6236"
    },
    "hljs-addition": {
        "color": "#22221b",
        "display": "inline-block",
        "width": "100%",
        "backgroundColor": "#7d9726"
    },
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "background": "#22221b",
        "color": "#929181",
        "padding": "0.5em"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}