# Changelog

## 15.6.1

- Fix: Allow override of `display:` styles when `wrapLongLines` is true

## 15.6.0
- Fix: #561 Fix bug with wrapLines that butchers highlighting
- Docs: #559 Adding languages
- Feat: #555 Add Vue language support
- Feat: #534 Add a11yOneLight theme

## [ gap in documentation ]

## 15.4.3 / 2020-12-07
- Fixed `highlight` version regression

## 15.4.2 / 2020-12-07
- Updated `lowlight` to 1.17.0 to match `highlight` dependency version

## 15.4.1 / 2020-12-07
- Bugfix: increased minwidth to avoid unequal line number widths
- Bugfix: prevent last line of file from rendering an unwanted line number

## 15.4.0 / 2020-12-07
- Security fix: updated highlight.js to 10.4.1

## 15.3.1 / 2020-11-24
- Bugfix: styling correctly applied to interpolation punctuation

## 15.3.0 / 2020-10-29
- Updated prismjs (1.22.0) and refractor (3.2.0)
- Updated prism-themes (1.5.0)
- Fixed a broken readme link

## 15.2.1 / 2020-10-08
- Bugfix: use string templating in language-specific theming code

## 15.2.0 / 2020-10-07
- Allow language-specific theme styles to override default theme styles

## 15.1.0 / 2020-10-05
- Updated to `prism-themes` 1.4.1

## 15.0.1 / 2020-10-03
- Regenerate Prism themes containing nested tokens

## 15.0.0 / 2020-10-03
- Breaking change: Allow styling nested tokens with classes in styles (probably not practically breaking for most, since this is a change that fixes a previously non-working feature)

## 14.0.2 / 2020-10-03
- updated test snapshots after 14.0.1 fix
- added tests for line number rendering behavior

## 14.0.1 / 2020-10-03
- Bugfix: Show line numbers only when `showLineNumbers == true`

## 14.0.0 / 2020-10-01
- New prop: `wrapLongLines`, which removes the need to manually add `white-space: pre` styling
- New default prop value: `showInlineLineNumbers = true`: when `showLineNumbers` is `true`, we now default to inline line numbers instead of putting them in a separate `<code>` block
- Explicit white-space styling on `<code>` tag (will be set to either `white-space: pre` or `white-space: pre-wrap` depending on value of `wrapLongLines` prop)

## 13.5.3 / 2020-09-03
- don't forget to add current version to CHANGELOG at release time :)

## 13.5.2 / 2020-09-03
- filled in CHANGELOG for v10.x through v13.5.1

## 13.5.1 / 2020-08-19
- Brought back `createElement` that went missing as of version 13.5.

```js
import createElement from "react-syntax-highlighter/create-element";
```

## 13.5.0 / 2020-08-17
- Cleaned up old files in project root, add jest to eslint
- Updated to `refractor` 3.1.0, which brings in `prismjs` 1.21.0. We'd pinned `react-syntax-highlighter 13.3.1` to `prismjs 1.21.0`, but didn't realize that `refractor`'s dependency would keep us at 1.20.0

## 13.4.0 / 2020-08-14
- added ESLint (https://github.com/react-syntax-highlighter/react-syntax-highlighter/pull/298)
- added favicon to demo (https://github.com/react-syntax-highlighter/react-syntax-highlighter/pull/295)
- new addition to the "built with" list (https://github.com/react-syntax-highlighter/react-syntax-highlighter/pull/293)

### Bugfixes
- JS error when using lineNumberStyle() for inline line numbers
- incorrect 'hljs' className applied to `<pre>` when using Prism

## 13.3.1 / 2020-08-08
- `prism` updated to 1.21.0, with lots of improvements: [CHANGELOG for prism 1.21.0](https://github.com/PrismJS/prism/blob/master/CHANGELOG.md#1210-2020-08-06)
- added Github repo link to the upper right corner of demo pages

### Bugfixes
- Truncated Prism `shell-session` language command output
- Incorrect syntax highlighting for mutli-line comments
- Unexpected behavior with `useInlineStyles={false}`

## 13.2.1 / 2020-07-30
### Bugfixes
- Neglected to include rebuilt "Prism async light" demo files in 13.2.0 release

## 13.2.0 / 2020-07-30
- Updated our `prism-themes` dependency to pull in new themes, including `vsc-dark-plus`
- [Prism async demo](https://react-syntax-highlighter.github.io/react-syntax-highlighter/demo/prism-async-light.html) now dynamically loads autogenerated Prism themes list (EDIT: neglected to include built demo files, fixed in 13.2.1)

### New Prism themes available

- `a11yDark`
- `dracula`
- `materialDark`
- `materialLight`
- `materialOceanic`
- `nord`
- `shadesOfPurple`
- `synthwave84`
- `vscDarkPlus`

## 13.1.1 / 2020-07-30
- Repaired demo builds for compatibility with Github Pages
- Moved build-generated demo files to their own subdirectory inside /demo
- updated repo URLs (we've migrated from `conorhastings/react-syntax-highlighter` to `react-syntax-highlighter/react-syntax-highlighter`)
- corrected a link to the `refractor` repo

## 13.1.0 / 2020-07-23

Demo updates:
- indicate that Highlight powers the default demo
- restored Prism demo that went missing in 13.0.0
- autogenerate demo-specific style lists (and add a comment to autogenerated files pointing out that they're autogenerated)
- demos now autoload full lists of languages and styles from highlight/prism
- minor UI adjustments

## 13.0.0 / 2020-07-23
- latest `highlight ^10.1.1` and `lowlight ^1.14.0` dependencies
- latest `refractor ^3.0.0` and `prism ^1.20.0` dependencies
- updated codecov and lodash deps
- corrected .gitignore
- updated/fixed tests
- new Demos UI
- CircleCi v2 config added
- exposed `createElement()` in public API
- change FUNDING platform from `liberapay` to `github`
- added projects to "built with" section of README
- updated `websocket-extensions` to 0.1.4

### New prop: `showInlineLineNumbers`

```jsx
<SyntaxHighlighter
  showLineNumbers={true} // required to show line numbers, whether inline or not
  showInlineLineNumbers={true} // render them inside wrapped lines, instead of as a separate <code> block
  renderer={virtualizedRenderer({
    rowHeight: 20
  })}
>
  {code}
</SyntaxHighlighter>
```

When paired with `showLineNumbers={true}`, `showInlineLineNumbers={true}` injects line numbers into each wrapped line of code, instead of creating a separate `<code></code>` block for the numbers. This allows line numbering to work with virtualized renderers such as `react-syntax-highlighter-virtualized-renderer`.

Inline line numbers are styled so that they're not selected when drag-selecting multiple lines of highlighted text, preserving expected behavior.

### Breaking changes
Highlight v10 introduced some breaking changes for us here which we've worked around (mainly that `getLanguage` was no longer exposed), but this shouldn't cause breakage for most people relying on `react-syntax-highlighter` and not consuming `highlight.js` directly.

#### Theme rename

- If you rely on the `darkula` theme from `highlight.js`, highlight v10 has renamed that style to `darcula`.

#### Renamed/removed highlight.js languages
- `cs` (renamed to `csharp`)
- `nimrod` (renamed to `nim`)
- `tex` (removed)

### New languages

> Note: `react-syntax-highlighter` provides an automated build wrapper around styles and languages provided by `highlight.js` (via `lowlight`) and `prism` (via `refractor`). Changes here are simply describing what's changed in those libraries. If you'd like to see another language or style added, please contribute to those libraries.

#### New highlight.js languages
- `cLike` (`c-like`)
- `c`
- `latex`
- `phpTemplate` (`php-template`)
- `pythonRepl` (`python-repl`)

#### New prism.js languages
- `abnf`
- `antlr4`
- `aql`
- `bbcode`
- `bnf`
- `brightscript`
- `cil`
- `cmake`
- `concurnas`
- `dax`
- `dnsZoneFile` (`dns-zone-file`)
- `ebnf`
- `ejs`
- `etlua`
- `excelFormula` (`excel-formula`)
- `factor`
- `firestoreSecurityRules` (`firestore-security-rules`)
- `ftl`
- `gcode`
- `gdscript`
- `gml`
- `hcl`
- `javadoc`
- `javadoclike`
- `javastacktrace`
- `jq`
- `jsExtras` (`js-extras`)
- `jsTemplates` (`js-templates`)
- `jsdoc`
- `json5`
- `jsonp`
- `latte`
- `lilypond`
- `llvm`
- `moonscript`
- `n1ql`
- `nand2tetrisHdl` (`nand2tetris-hdl`)
- `neon`
- `pascaligo`
- `pcaxis`
- `phpdoc`
- `powerquery`
- `qml`
- `regex`
- `robotframework`
- `shellSession` (`shell-session`)
- `solidity`
- `solutionFile` (`solution-file`)
- `sparql`
- `splunkSpl` (`splunk-spl`)
- `sqf`
- `t4Cs` (`t4-cs`)
- `t4Templating` (`t4-templating`)
- `t4Vb` (`t4-vb`)
- `toml`
- `turtle`
- `vala`
- `zig`


## 12.0.2 / 2019-12-15
- version bump, no other changes

## 12.0.1 / 2019-12-15
- version bump, no other changes

## 12.0.0 / 2019-12-15
- added projects to README
- updated Docker config: removed `/sbin/init` command
- introduced Github Actions config
- removed CircleCI config
- updated `mixin-deep` and `handlebars` dependencies
- updated "removed" code in diff demo

## 11.0.2 / 2019-07-13
- updated `style` prop description in README
- updated demo URL from `conor.rodeo` to `conorhastings.github.io`
- updated `lodash` dep from 4.17.11 to 4.17.14

## 11.0.1 / 2019-06-27
- version bump, no other changes

## 11.0.0 / 2019-06-27
- version bump, no other changes

## 10.3.3 / 2019-06-27
- build to update `async-languages` for highlight, picking up new languages added in 10.3.0

> Historical note: `vs-dark` theme was automatically removed from `AVAILABLE_STYLES_PRISM.MD` in build process here

## 10.3.2 / 2019-06-27
- version bump

## 10.3.1 / 2019-06-27
- minor code change to diff demo
- added FUNDING.yml
- minor README updates

## 10.3.0 / 2019-05-22
- added projects to "built with" section of README
- corrected README typos

### New highlight.js languages
- `angelscript`
- `arcade`
- `gml`
- `isbl`
- `pgsql`
- `plaintext`
- `properties`
- `reasonml`
- `sas`

### New prism.js styles
- `xonokai`
- `vs-dark`


## 10.2.1 / 2019-03-21
- fixed `classNames` concatenation in `createElement`
- updated test snapshots accordingly

## 10.2.0 / 2019-03-10
- updated license copyright date
- updated `highlight` from 9.12.0 to 9.13.0
- updated `lowlight` from 1.9.1 to 1.11.0

### New highlight.js styles available
- `a11yDark`
- `a11yLight`
- `anOldHope`
- `atomOneDarkReasonable`
- `gml`
- `isblEditorDark`
- `isblEditorLight`
- `lightfair`
- `nord`
- `shadesOfPurple`

## 10.1.3 / 2019-02-16
- README corrections
- updated deps: `@babel/runtime`, `babel-jest`, `codecov`, `jest`, `request`, `webpack-dev-server`
- updated import paths in demos

## 10.1.2 / 2018-12-06
- fine-tuned `prettier` config
- prettier cleanup of src and demo code
- use `@babel/runtime` instead of `babel-runtime`

## 10.0.1 / 2018-11-09
- All highlighters except for the old "-light" varieties now have a `supportedLanguages` field which can be used to determine the supported languages

## 10.0.0 / 2018-10-30
- `registerLanguage` is now a static method of the default export of highlighters that require registering of languages.
- `prism-async-light` & `light-async` now ship with their own language loaders
- Added `esm` & `cjs` outputs. Importing should now be done through import OR the import should point into the dist directory.
```
import SyntaxHighlighter from "react-syntax-highlighter/prism";

// Becomes:
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";

// OR (less ideally)
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter/dist/cjs/prism";
// OR
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter/dist/esm/prism";
```
- styles have moved into the `dist` directory. Update your imports from:

```
import style from 'react-syntax-highlighter/styles/prism'
```

to:

```
import style from 'react-syntax-highlighter/dist/styles/prism'
```

## 9.0.1 / 2018-10-22
- fixed code-splitting in webpack 3.

## 9.0.0 / 2018-10-07
- Added async loaded versions of prism & highlight


## 8.0.1 / 2018-07-05
- properly propagate `className` argument to `createLineElement` when `wrapLines=true`

## 8.0.0 / 2018-07-03
- pass along classNames that are not part of stylesheet (this allows passing in `className` in `lineProps`)

## 7.0.4 / 2018-05-02
- update `refractor` version

## 7.0.3 / 2018-05-02
- updated `.npmignore` to make it include less unneccsary files.
- update example in README to show proper path for require styles

## 7.0.2 / 2018-01-25
- handle fs errors in language and stylesheet build scripts
- fix link to `refractor` in README

## 7.0.1 / 2018-01-25
- removed claims of sanity from README

## 7.0.0 / 2018-01-25
- add lineProps prop which takes either an object or a function that returns an object that is then passed onto each line created when wrapLines=true
- removed lineStyle prop the behavior of this prop can be replicated with lineProps passing a style object inside the props object. this is a breaking change

## 6.1.2 / 2018-01-17
- add `.babelrc` to `.npmignore` for parcel support

## 6.1.1 / 2017-12-09
- move `prism-themes` to dev dependencies

## 6.1.0 / 2017-12-09
- added light version support for prism (refractor) based highlighting

## 6.0.4 / 2017-11-22
- fixed vendor prefixing for prism styles

## 6.0.3 / 2017-11-17
- add links to available languages in README (by @adamweeks)
- fix line number display for prism highlighter

## 6.0.2 / 2017-11-16
- fix git links in package.json

## 6.0.1 / 2017-11-16
- update package main to be root instead of dist

## 6.0.0 / 2017-11-16
- add support for alternative syntax highlighting using ast generated from prismjs via <a href="https://github.com/wooorm/refractor">refractor</a> (by @conorhastings and @maxmcd)

## 5.8.1 / 2017-10-26
- avoid unneeded function calls if language is text
- note text language in readme

## 5.8.0 / 2017-10-26
- avoid unneeded function calls if language is text
- note text language in readme

## 5.8.0 / 2017-10-25
- add support for text as valid language

## 5.7.1 / 2017-10-26
- check if children is array and use children[0] if so.

## 5.7.0 / 2017-08-21
- check if we have language before attempting to highlight.
- add test for unknown language to avoid further issues with content not rendering


## 5.6.3 / 2017-08-12
- added code of conduct
- update dependency versions

## 5.6.2 / 2017-05-29
- fix edge cases in line wrapping feature

## 5.6.0 / 2017-05-11
- add list of available languages (by @forresto)

## 5.5.2 / 2017-05-07
- render plain string if no language present

## 5.5.1 / 2017-05-05
- add license file
- update readme

## 5.50 / 2017-04-23
- added test for useInlineStyles=false
- export previously unexported functions from createElemeent

## 5.4.1 / 2017-04-23
- add code coverage

## 5.4.0 / 2017-04-23
- add code coverage


## 5.2.0 / 2017-04-08
- flatten tree before using wrapLines function
- allow using a custom renderer without wrapLines

## 5.1.3 / 2017-04-02
- flatten tree before using wrapLines function
- allow using a custom renderer without wrapLines
- update broken links in demo
- handle some edge cases in wrapLines function
- generalize function for finding text child


## 5.1.2 / 2017-03-21
- update version of react-syntax-highlighter-virtualized-renderer for demo
- handle some edge cases in wrapLines function

## 5.1.1 / 2017-03-21
- add api to allow for use of custom renderer (virtualized, native, etc...)

## 5.1.0 / 2017-03-21
- add api to allow for use of custom renderer (virtualized, native, etc...)

## 5.0.0 / 2017-02-12
- allow wrapping of individual element in <span> tag

## 4.0.1 / 2016-12-09
- allow styling individual numbers

## 3.0.2 / 2016-12-04
- add support for IE and older android browsers (by @yahiousun)

## 3.0.1 / 2016-12-03
- update dependencies

## 3.0.0 / 2016-10-29
- wrap individual line numbers in span
- removed envified light build in favor of only using seperate entry point

## 2.11.0 / 2016-10-27
- export lowlight registerLanguage in light build

## 2.10.0 / 2016-10-09
- add section to readme showcasing projects built with react syntax highlighter
- fix style assignment function to not end up with unexpected styles


## 2.9.0 / 2016-10-06
- call highlightAuto if no language provided
- unify quote style in code

## 2.8.0 / 2016-10-02
- add support for line numbers


## 2.7.1 / 2016-10-02
- fix require of default style

## 2.7.0 / 2016-09-24
- add jest snapshot testing
- add more available styles

## 2.6.1 / 2016-09-22
- add new info on light build to readme
- don't wrap text in spans

## 2.6.0 / 2016-09-16
- add a seperate entry point for light build (by @bmathews)

## 2.5.0 / 2016-09-15
- add codeTagProps to props to allow passing arbitrary orios to code tag

## 2.4.0 / 2016-09-11
- allow optionally applying css classNames instead of inline styles
- fix light build example

## 2.3.0 / 2016-08-27
- add customStyle prop
- update available styles


## 2.2.0 / 2016-08-27
- allow light build via env variable

## 2.1.1 / 2016-07-02
- fix warning by not passing invalid non dom props to pre tag

## 2.1.0 / 2016-05-20
- update depedndencies

## 2.0.4 / 2016-05-18
- strict dependency on highlight

## 2.0.3 / 2016-05-07
- update dependency and add new styles

## 2.0.2 / 2016-04-19
- fix bad import in readme

## 2.0.1 / 2016-04-19
- fix missing quotes around import location in readme example

## 2.0.0 / 2016-04-17
- have user pass in style object instead of string of style name

## 1.3.0 / 2016-04-03
- make peerDependency of react more liberal

## 1.2.0 / 2016-03-27
- code style changes
- add new highlight.js styles

## 1.1.2 / 2016-01-31
- remove unused createTextElement function

## 1.1.1 / 2016-01-31
- use a Text component instead of createTextElement function
- explain js styles in readme

## 1.1.0 / 2016-01-30
- use default top level style from highightjs
- fix spelling of segment in code (by @winkler1)

## 1.0.2 / 2016-01-29
- update package to allow any version of react 14

## 1.0.1 / 2016-01-29
- wrap children in code element as well as pre element

## 1.0.0 / 2016-01-28
- add script to build javascript styles
- write readme
- allow changing style in demo

## 0.0.2 / 2016-01-28
- add script to build javascript styles
- write readme
- update lowlight version
- use pre not span
- pass along optional props to pre tag

## 0.0.1 / 2016-01-26
- initial version
- syntax highlighting using virtual dom created by lowlight
