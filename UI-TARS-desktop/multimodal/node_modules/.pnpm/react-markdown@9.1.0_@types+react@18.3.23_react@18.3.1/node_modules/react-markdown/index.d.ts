export type AllowElement = import("./lib/index.js").AllowElement;
export type Components = import("./lib/index.js").Components;
export type ExtraProps = import("./lib/index.js").ExtraProps;
export type Options = import("./lib/index.js").Options;
export type UrlTransform = import("./lib/index.js").UrlTransform;
export { MarkdownAsync, MarkdownHooks, Markdown as default, defaultUrlTransform } from "./lib/index.js";
//# sourceMappingURL=index.d.ts.map