import { API_BASE_URL, API_ENDPOINTS } from '@/common/constants';
import { AgentEventStream, SessionMetadata } from '@/common/types';
import { AgentTARSServerVersionInfo } from '@agent-tars/interface';
import { ChatCompletionContentPart } from '@multimodal/agent-interface';
import { socketService } from './socketService';

/**
 * API Service - Handles HTTP requests to the Agent TARS Server
 *
 * Provides methods for:
 * - Session management (create, get, update, delete)
 * - Query execution (streaming and non-streaming)
 * - Server health checks
 * - Version information
 */
class ApiService {
  /**
   * Check server health status
   */
  async checkServerHealth(): Promise<boolean> {
    try {
      // Try ping through socket if connected
      if (socketService.isConnected()) {
        const pingSuccessful = await socketService.ping();
        if (pingSuccessful) return true;
      }

      // Fall back to API health endpoint
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.HEALTH}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal: AbortSignal.timeout(3000),
      });

      return response.ok;
    } catch (error) {
      console.error('Error checking server health:', error);
      return false;
    }
  }

  /**
   * Create a new session (adapted for Goose API)
   */
  async createSession(): Promise<SessionMetadata> {
    try {
      // For Goose, we create sessions locally since the API doesn't support session creation
      const sessionName = `session_${Date.now()}`;

      // Return a mock session metadata for Goose
      return {
        id: sessionName,
        name: sessionName,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        workingDirectory: '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro',
        messageCount: 0
      };
    } catch (error) {
      console.error('Error creating session:', error);
      // Always return a session even if there's an error
      const fallbackSessionName = `session_${Date.now()}`;
      return {
        id: fallbackSessionName,
        name: fallbackSessionName,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        workingDirectory: '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro',
        messageCount: 0
      };
    }
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/sessions/${sessionId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok && response.status !== 404) {
        throw new Error(`Failed to delete session: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting session:', error);
      // For Goose, session deletion might not be supported via API
      // This is acceptable as sessions are managed by Goose internally
    }
  }

  /**
   * Get all sessions (adapted for Goose API)
   */
  async getSessions(): Promise<SessionMetadata[]> {
    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.SESSIONS}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        throw new Error(`Failed to get sessions: ${response.statusText}`);
      }

      const { sessions } = await response.json();

      // Convert Goose session format to TARS format
      return sessions.map((session: any) => ({
        id: session.name, // Use name as ID
        name: session.description || session.name,
        createdAt: Date.now(), // Goose doesn't provide timestamps
        updatedAt: Date.now(),
        workingDirectory: session.working_dir,
        messageCount: session.message_count || 0
      }));
    } catch (error) {
      console.error('Error getting sessions:', error);
      throw error;
    }
  }

  /**
   * Get details for a specific session (adapted for Goose API)
   */
  async getSessionDetails(sessionId: string): Promise<SessionMetadata> {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.SESSION_DETAILS}/${sessionId}`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: AbortSignal.timeout(5000), // Add 5 second timeout
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to get session details: ${response.statusText}`);
      }

      const data = await response.json();

      // Convert Goose session format to TARS format
      return {
        id: sessionId,
        name: data.metadata?.description || sessionId,
        createdAt: Date.now(), // Goose doesn't provide timestamps
        updatedAt: Date.now(),
        workingDirectory: data.metadata?.working_dir || '',
        messageCount: data.metadata?.message_count || 0
      };
    } catch (error) {
      console.error(`Error getting session details (${sessionId}):`, error);
      throw error;
    }
  }

  /**
   * Get events for a specific session (adapted for Goose API)
   */
  async getSessionEvents(sessionId: string): Promise<AgentEventStream.Event[]> {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.SESSION_EVENTS}/${sessionId}`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: AbortSignal.timeout(5000), // Add 5 second timeout
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to get session events: ${response.statusText}`);
      }

      const data = await response.json();

      // Convert Goose messages to TARS events format
      const events: AgentEventStream.Event[] = [];
      if (data.messages && Array.isArray(data.messages)) {
        data.messages.forEach((message: any, index: number) => {
          events.push({
            id: `event_${index}`,
            type: 'message',
            timestamp: Date.now(),
            data: {
              role: message.role || 'user',
              content: message.content || '',
              message: message.content || ''
            }
          });
        });
      }

      return events;
    } catch (error) {
      console.error(`Error getting session events (${sessionId}):`, error);
      throw error;
    }
  }

  /**
   * Get current status of a session (adapted for Goose API)
   */
  async getSessionStatus(sessionId: string): Promise<{ isProcessing: boolean; state: string }> {
    try {
      // For Goose, we'll return a default status since it doesn't have real-time status
      return {
        isProcessing: false,
        state: 'idle'
      };
    } catch (error) {
      console.error(`Error getting session status (${sessionId}):`, error);
      throw error;
    }
  }

  /**
   * Update session metadata (适配Goose - 使用本地存储)
   */
  async updateSessionMetadata(
    sessionId: string,
    updates: { name?: string; tags?: string[] },
  ): Promise<SessionMetadata> {
    try {
      // Goose后端不支持会话更新，改为本地存储
      console.log('[Session Update] Goose不支持会话更新，使用本地存储:', { sessionId, updates });

      // 将会话更新保存到localStorage
      const storageKey = `goose_session_${sessionId}`;
      const existingData = localStorage.getItem(storageKey);
      const sessionData = existingData ? JSON.parse(existingData) : {};

      const updatedData = {
        ...sessionData,
        ...updates,
        updatedAt: Date.now()
      };

      localStorage.setItem(storageKey, JSON.stringify(updatedData));
      console.log('[Session Update] 会话数据已保存到本地存储');

      // 返回更新后的会话元数据
      return {
        id: sessionId,
        name: updates.name || sessionData.name || sessionId,
        createdAt: sessionData.createdAt || Date.now(),
        updatedAt: Date.now(),
        workingDirectory: sessionData.workingDirectory || '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro',
        tags: updates.tags || sessionData.tags || [],
        messageCount: sessionData.messageCount || 0
      };
    } catch (error) {
      console.error(`Error updating session (${sessionId}):`, error);
      // 返回默认会话数据而不是抛出错误
      return {
        id: sessionId,
        name: sessionId,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        workingDirectory: '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro',
        messageCount: 0
      };
    }
  }



  /**
   * Send a streaming query (adapted for Goose WebSocket)
   */
  async sendStreamingQuery(
    sessionId: string,
    query: string | ChatCompletionContentPart[],
    onEvent: (event: AgentEventStream.Event) => void,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Create WebSocket connection to Goose
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.hostname}:8888/ws`;

        const socket = new WebSocket(wsUrl);

        // Track accumulated response content for streaming
        let accumulatedContent = '';
        let responseEventId = `response_${Date.now()}`;
        let messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('Received Goose message:', data);

            // Handle different Goose message types
            switch (data.type) {
              case 'response':
                // Streaming AI response - accumulate content
                if (data.content) {
                  accumulatedContent += data.content;

                  const responseEvent: AgentEventStream.Event = {
                    id: responseEventId,
                    type: 'assistant_streaming_message',
                    timestamp: Date.now(),
                    content: data.content, // Send the delta content, not accumulated
                    isComplete: false,
                    messageId: messageId
                  };
                  onEvent(responseEvent);
                }
                break;

              case 'tool_request':
                // Tool execution request - 增强工具请求处理
                const toolRequestEvent: AgentEventStream.Event = {
                  id: `tool_request_${Date.now()}`,
                  type: 'tool_call',
                  timestamp: Date.now(),
                  toolCallId: data.tool_call_id || `tool_${Date.now()}`,
                  name: data.tool_name || 'unknown_tool',
                  arguments: JSON.stringify(data.tool_args || {})
                };
                onEvent(toolRequestEvent);

                // 增强日志记录，帮助调试工具调用
                console.log('[Tool Request] 工具调用开始:', {
                  name: data.tool_name,
                  toolCallId: data.tool_call_id,
                  args: data.tool_args,
                  timestamp: Date.now()
                });
                break;

              case 'tool_response':
                // Tool execution result - 增强工具结果处理
                const toolResponseEvent: AgentEventStream.Event = {
                  id: `tool_response_${Date.now()}`,
                  type: 'tool_result',
                  timestamp: Date.now(),
                  toolCallId: data.tool_call_id || `tool_${Date.now()}`,
                  name: data.tool_name || 'unknown_tool',
                  content: data.content || data.result || '',
                  elapsedMs: data.elapsed_ms || 0
                };
                onEvent(toolResponseEvent);

                // 增强日志记录，帮助调试工具调用状态
                console.log('[Tool Response] 工具执行完成:', {
                  name: data.tool_name,
                  toolCallId: data.tool_call_id,
                  contentLength: (data.content || data.result || '').length,
                  elapsed: data.elapsed_ms,
                  hasError: !!data.error
                });
                break;

              case 'thinking':
                // AI thinking process
                const thinkingEvent: AgentEventStream.Event = {
                  id: `thinking_${Date.now()}`,
                  type: 'assistant_thinking_message',
                  timestamp: Date.now(),
                  content: data.content || ''
                };
                onEvent(thinkingEvent);
                break;

              case 'error':
                // Error message
                const errorEvent: AgentEventStream.Event = {
                  id: `error_${Date.now()}`,
                  type: 'system',
                  timestamp: Date.now(),
                  content: data.error || data.message || 'Unknown error',
                  level: 'error'
                };
                onEvent(errorEvent);
                break;

              case 'complete':
                // Conversation complete - send final streaming message with isComplete=true
                if (accumulatedContent) {
                  const finalStreamingEvent: AgentEventStream.Event = {
                    id: `${responseEventId}_final`,
                    type: 'assistant_streaming_message',
                    timestamp: Date.now(),
                    content: '', // Empty content for completion signal
                    isComplete: true,
                    messageId: messageId
                  };
                  onEvent(finalStreamingEvent);

                  // Also send the final assistant message
                  const finalResponseEvent: AgentEventStream.Event = {
                    id: `final_${responseEventId}`,
                    type: 'assistant_message',
                    timestamp: Date.now(),
                    content: accumulatedContent,
                    finishReason: 'stop',
                    messageId: messageId
                  };
                  onEvent(finalResponseEvent);
                }

                const completeEvent: AgentEventStream.Event = {
                  id: `complete_${Date.now()}`,
                  type: 'agent_run_end',
                  timestamp: Date.now()
                };
                onEvent(completeEvent);
                socket.close();
                resolve();
                break;

              default:
                // Unknown message type, treat as general message
                const defaultEvent: AgentEventStream.Event = {
                  id: `event_${Date.now()}`,
                  type: 'assistant_message',
                  timestamp: Date.now(),
                  content: data.content || data.message || JSON.stringify(data),
                  finishReason: 'stop'
                };
                onEvent(defaultEvent);
                break;
            }
          } catch (e) {
            console.error('Error parsing WebSocket message:', e);
            // Send error event to UI
            const errorEvent: AgentEventStream.Event = {
              id: `parse_error_${Date.now()}`,
              type: 'system',
              timestamp: Date.now(),
              content: 'Failed to parse WebSocket message',
              level: 'error'
            };
            onEvent(errorEvent);
          }
        };

        socket.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);

          // Send disconnect event to UI
          const disconnectEvent: AgentEventStream.Event = {
            id: `disconnect_${Date.now()}`,
            type: 'system',
            timestamp: Date.now(),
            content: 'Connection closed',
            level: 'info'
          };
          onEvent(disconnectEvent);

          // Only resolve if it's a normal closure
          if (event.code === 1000) {
            resolve();
          } else {
            // Abnormal closure, could implement retry logic here
            console.warn('WebSocket closed abnormally:', event.code);
            resolve(); // For now, still resolve to prevent hanging
          }
        };

        socket.onerror = (error) => {
          console.error('WebSocket error:', error);

          // Send error event to UI
          const errorEvent: AgentEventStream.Event = {
            id: `connection_error_${Date.now()}`,
            type: 'error',
            timestamp: Date.now(),
            data: {
              error: 'WebSocket connection error',
              message: 'Failed to connect to Goose backend',
              role: 'system'
            }
          };
          onEvent(errorEvent);

          reject(new Error('WebSocket connection failed'));
        };

        // Add connection timeout
        const connectionTimeout = setTimeout(() => {
          if (socket.readyState === WebSocket.CONNECTING) {
            socket.close();
            reject(new Error('WebSocket connection timeout'));
          }
        }, 10000); // 10 second timeout

        socket.onopen = () => {
          clearTimeout(connectionTimeout);
          console.log('WebSocket connected to Goose');

          // Send connection success event
          const connectEvent: AgentEventStream.Event = {
            id: `connect_${Date.now()}`,
            type: 'system',
            timestamp: Date.now(),
            data: {
              message: 'Connected to Goose backend',
              role: 'system',
              status: 'connected'
            }
          };
          onEvent(connectEvent);

          // Send message through WebSocket
          const message = typeof query === 'string' ? query : JSON.stringify(query);
          socket.send(JSON.stringify({
            type: 'message',
            session_id: sessionId,
            content: message,
            timestamp: Date.now()
          }));
        };

      } catch (error) {
        console.error('Error in streaming query:', error);
        reject(error);
      }
    });
  }

  /**
   * Send a non-streaming query
   */
  async sendQuery(sessionId: string, query: string | ChatCompletionContentPart[]): Promise<string> {
    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.QUERY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, query }),
      });

      if (!response.ok) {
        throw new Error(`Failed to send query: ${response.statusText}`);
      }

      const data = await response.json();
      return data.result;
    } catch (error) {
      console.error('Error sending query:', error);
      throw error;
    }
  }

  /**
   * Abort a running query
   */
  async abortQuery(sessionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.ABORT}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId }),
      });

      if (!response.ok) {
        throw new Error(`Failed to abort query: ${response.statusText}`);
      }

      const data = await response.json();
      return data.success;
    } catch (error) {
      console.error('Error aborting query:', error);
      throw error;
    }
  }

  /**
   * Generate a summary for a conversation
   */
  async generateSummary(sessionId: string, messages: any[]): Promise<string> {
    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.GENERATE_SUMMARY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, messages }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate summary: ${response.statusText}`);
      }

      const { summary } = await response.json();
      return summary;
    } catch (error) {
      console.error('Error generating summary:', error);
      return 'Untitled Conversation';
    }
  }

  /**
   * Get application version information including git hash
   * In replay mode, use injected version info instead of making API request
   */
  async getVersionInfo(): Promise<AgentTARSServerVersionInfo> {
    // Check if version info is injected in replay/share mode
    if (window.AGENT_TARS_VERSION_INFO) {
      return window.AGENT_TARS_VERSION_INFO;
    }

    // Fallback to API request for normal mode
    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.VERSION}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal: AbortSignal.timeout(3000),
      });

      if (!response.ok) {
        throw new Error(`Failed to get version info: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        version: data.version || '0.0.0',
        buildTime:
          typeof data.buildTime === 'string'
            ? parseInt(data.buildTime)
            : data.buildTime || Date.now(),
        gitHash: data.gitHash,
      };
    } catch (error) {
      console.error('Error getting version info:', error);
      return { version: '0.0.0', buildTime: Date.now() };
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
